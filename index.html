<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Weather App</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <main class="container">
      <h1>Weather</h1>

      <form id="weatherForm" class="search" autocomplete="off">
        <label for="locationInput" class="sr-only">Location</label>
        <input id="locationInput" name="location" type="text" placeholder="Enter a city or place (e.g., London)" required />
        <button type="submit" id="searchBtn">Get Weather</button>
      </form>

      <p id="status" class="status" aria-live="polite"></p>
      <ul id="results" class="results" role="listbox" hidden></ul>


      <article id="weather" class="card" hidden>
        <header>
          <h2 id="place">—</h2>
        </header>
        <section class="current">
          <div class="main-weather">
            <img id="icon" class="icon" src="" alt="Weather icon" role="img" aria-label="Weather icon">
            <div class="main-details">
              <div id="temp" class="temp">—</div>
              <div id="feels-like" class="feels-like">Feels like —</div>
              <div id="desc" class="desc">—</div>
            </div>
          </div>
          <div class="weather-details">
            <div class="detail-item">
              <span class="detail-label">Humidity</span>
              <span id="humidity" class="detail-value">—</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Pressure</span>
              <span id="pressure" class="detail-value">—</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Wind</span>
              <span id="wind" class="detail-value">—</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Visibility</span>
              <span id="visibility" class="detail-value">—</span>
            </div>
          </div>
          <div class="sun-times">
            <div class="sun-item">
              <span class="sun-label">🌅 Sunrise</span>
              <span id="sunrise" class="sun-time">—</span>
            </div>
            <div class="sun-item">
              <span class="sun-label">🌇 Sunset</span>
              <span id="sunset" class="sun-time">—</span>
            </div>
          </div>
        </section>
      </article>

      <footer class="footer">
        <small>Data by <a href="https://openweathermap.org/" target="_blank" rel="noreferrer noopener">OpenWeather</a></small>
      </footer>
    </main>

    <script src="script.js" defer></script>
  </body>
</html>

