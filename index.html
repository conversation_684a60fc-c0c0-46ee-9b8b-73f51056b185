<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Weather App</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <main class="container">
      <h1>Weather</h1>

      <form id="weatherForm" class="search" autocomplete="off">
        <label for="locationInput" class="sr-only">Location</label>
        <input id="locationInput" name="location" type="text" placeholder="Enter a city or place (e.g., London)" required />
        <button type="submit" id="searchBtn">Get Weather</button>
      </form>

      <p id="status" class="status" aria-live="polite"></p>
      <ul id="results" class="results" role="listbox" hidden></ul>


      <article id="weather" class="card" hidden>
        <header>
          <h2 id="place">—</h2>
        </header>
        <section class="current">
          <div id="icon" class="icon" role="img" aria-label="Weather icon">⛅</div>
          <div class="details">
            <div id="temp" class="temp">—</div>
            <div id="desc" class="desc">—</div>
          </div>
        </section>
      </article>

      <footer class="footer">
        <small>Data by <a href="https://open-meteo.com/" target="_blank" rel="noreferrer noopener">Open‑Meteo</a></small>
      </footer>
    </main>

    <script src="script.js" defer></script>
  </body>
</html>

