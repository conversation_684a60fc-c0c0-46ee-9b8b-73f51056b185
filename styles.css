@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

:root {
  --bg: #0f172a;
  --panel: #111827;
  --text: #e5e7eb;
  --muted: #9ca3af;
  --accent: #22d3ee;
  --accent-2: #60a5fa;
}

* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  background: radial-gradient(1200px 800px at 20% -10%, rgba(96,165,250,.15), transparent 60%),
              radial-gradient(1000px 600px at 100% 10%, rgba(34,211,238,.18), transparent 50%),
              var(--bg);
  color: var(--text);
}
.container {
  max-width: 720px;
  margin: 0 auto;
  padding: 32px 16px 48px;
}

h1 { font-size: 32px; margin: 8px 0 20px; }

.search {
  display: flex; gap: 8px; align-items: center;
  background: rgba(255,255,255,0.04);
  padding: 12px; border-radius: 12px; backdrop-filter: blur(6px);
}
.search input {
  flex: 1; min-width: 0;
  background: transparent; border: none; outline: none;
  color: var(--text); font-size: 16px; padding: 10px 8px;
}
.results { list-style: none; margin: 0 0 12px; padding: 0; }
.results[hidden] { display: none; }
.results li { cursor: pointer; padding: 8px 12px; border-radius: 8px; }
.results li:hover, .results li:focus { background: rgba(255,255,255,0.08); outline: none; }

.search button {
  background: linear-gradient(135deg, var(--accent), var(--accent-2));
  color: #00111a; border: none; font-weight: 700;
  padding: 10px 14px; border-radius: 10px; cursor: pointer;
}
.search button:hover { filter: brightness(1.05); }
.search button:active { transform: translateY(1px); }

.status { min-height: 24px; color: var(--muted); margin: 8px 4px 16px; }

.card {
  background: rgba(17,24,39,0.7);
  border: 1px solid rgba(255,255,255,0.06);
  border-radius: 16px; padding: 18px; box-shadow: 0 10px 30px rgba(0,0,0,.35);
}

.current {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-weather {
  display: flex;
  gap: 16px;
  align-items: center;
}

.icon {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.main-details {
  flex: 1;
}

.temp {
  font-size: 44px;
  font-weight: 700;
  line-height: 1;
}

.feels-like {
  font-size: 14px;
  color: var(--muted);
  margin-top: 4px;
}

.desc {
  color: var(--muted);
  margin-top: 6px;
  text-transform: capitalize;
  font-size: 16px;
}

.weather-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  color: var(--muted);
  font-size: 14px;
}

.detail-value {
  font-weight: 600;
  font-size: 14px;
}

.sun-times {
  display: flex;
  justify-content: space-around;
  padding: 12px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
}

.sun-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.sun-label {
  font-size: 12px;
  color: var(--muted);
}

.sun-time {
  font-weight: 600;
  font-size: 14px;
}

.footer { margin-top: 24px; color: var(--muted); }
.footer a { color: var(--accent-2); }

.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); white-space: nowrap; border: 0; }

