# Weather Application

A modern, responsive weather application built with vanilla JavaScript that provides comprehensive weather information using the OpenWeather API.

*Originally developed by <PERSON> for Unified Mentor Weather Application Project.*

## Features

- 🌍 **Location Search**: Search for weather by city name with intelligent autocomplete
- 🌡️ **Current Weather**: Temperature, feels-like temperature, and weather description
- 💧 **Detailed Metrics**: Humidity, atmospheric pressure, wind speed & direction, visibility
- 🌅 **Sun Times**: Sunrise and sunset times for the selected location
- 🎨 **Modern UI**: Clean, responsive design with weather icons
- 📱 **Mobile Friendly**: Works seamlessly on desktop and mobile devices

## Setup

1. **Get an API Key**:
   - Visit [OpenWeather API](https://openweathermap.org/api)
   - Sign up for a free account
   - Generate your API key

2. **Configure the Application**:
   - Open `script.js`
   - Replace `'YOUR_API_KEY_HERE'` with your actual OpenWeather API key:
   ```javascript
   const OPENWEATHER_API_KEY = 'your_actual_api_key_here';
   ```

3. **Run the Application**:
   - Open `index.html` in your web browser
   - Start searching for weather information!

## Usage

1. Enter a city name in the search box
2. Select from the suggested locations if multiple results appear
3. View comprehensive weather information including:
   - Current temperature and feels-like temperature
   - Weather description with icon
   - Humidity and atmospheric pressure
   - Wind speed and direction
   - Visibility
   - Sunrise and sunset times

## Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **Vanilla JavaScript**: No frameworks, pure JavaScript
- **OpenWeather API**: Weather data and geocoding
- **Responsive Design**: Mobile-first approach

## API Endpoints Used

- **Geocoding**: `http://api.openweathermap.org/geo/1.0/direct`
- **Current Weather**: `https://api.openweathermap.org/data/2.5/weather`
- **Weather Icons**: `https://openweathermap.org/img/wn/`

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge