// Simple weather app using Open-Meteo (no API key required)
// Flow:
// 1) Geocode the user-entered place to lat/lon via Open-Meteo geocoding
// 2) Fetch current weather via Open-Meteo forecast API with current_weather=true
// 3) Map weathercode to description and emoji icon

const form = document.getElementById('weatherForm');
const input = document.getElementById('locationInput');
const statusEl = document.getElementById('status');
const resultsEl = document.getElementById('results');
const card = document.getElementById('weather');
const placeEl = document.getElementById('place');
const tempEl = document.getElementById('temp');
const descEl = document.getElementById('desc');
const iconEl = document.getElementById('icon');

form.addEventListener('submit', async (e) => {
  e.preventDefault();
  const q = input.value.trim();
  if (!q) return;

  resetUI();
  setStatus('Searching…');

  try {
    const choices = await geocodeMany(q);
    if (!choices || choices.length === 0) {
      setStatus('No results. Try a different place.');
      return;
    }

    if (choices.length === 1) {
      selectPlace(choices[0]);
      return;
    }

    // Show chooser
    showResults(choices);
    setStatus('Select a location');
  } catch (err) {
    console.error(err);
    setStatus('Something went wrong. Please try again.');
  }
});

function resetUI() {
  card.hidden = true;
  hideResults();
  iconEl.textContent = '⛅';
  placeEl.textContent = '—';
  tempEl.textContent = '—';
  descEl.textContent = '—';
}

function setStatus(msg) {
  statusEl.textContent = msg;
}

function showResults(choices) {
  resultsEl.innerHTML = '';
  resultsEl.hidden = false;
  choices.slice(0, 8).forEach((c, idx) => {
    const li = document.createElement('li');
    li.tabIndex = 0;
    li.role = 'option';
    li.ariaSelected = 'false';
    li.textContent = formatPlace(c);
    li.addEventListener('click', () => selectPlace(c));
    li.addEventListener('keydown', (ev) => {
      if (ev.key === 'Enter' || ev.key === ' ') selectPlace(c);
    });
    resultsEl.appendChild(li);
    if (idx === 0) li.focus();
  });
}

function hideResults() {
  resultsEl.hidden = true;
  resultsEl.innerHTML = '';
}

function formatPlace(c) {
  const parts = [c.name, c.admin1, c.country].filter(Boolean);
  return parts.join(', ');
}

async function geocodeMany(query) {
  const url = new URL('https://geocoding-api.open-meteo.com/v1/search');
  url.searchParams.set('name', query);
  url.searchParams.set('count', '8');
  url.searchParams.set('language', 'en');
  url.searchParams.set('format', 'json');
  const res = await fetch(url.toString());
  if (!res.ok) throw new Error('Geocoding failed');
  const data = await res.json();
  return data.results || [];
}

async function selectPlace(geo) {
  hideResults();
  const { name, country, latitude, longitude } = geo;
  setStatus('Fetching weather…');
  const weather = await getCurrentWeather(latitude, longitude);
  if (!weather) {
    setStatus('Could not fetch weather.');
    return;
  }
  renderWeather({
    place: [name, country].filter(Boolean).join(', '),
    tempC: weather.temperature,
    code: weather.weathercode,
  });
  setStatus('');
}

async function geocode(query) {
  const url = new URL('https://geocoding-api.open-meteo.com/v1/search');
  hideResults();

  url.searchParams.set('name', query);
  url.searchParams.set('count', '1');
  url.searchParams.set('language', 'en');
  url.searchParams.set('format', 'json');

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error('Geocoding failed');
  const data = await res.json();
  return data.results && data.results.length ? data.results[0] : null;
}

async function getCurrentWeather(lat, lon) {
  const url = new URL('https://api.open-meteo.com/v1/forecast');
  url.searchParams.set('latitude', String(lat));
  url.searchParams.set('longitude', String(lon));
  url.searchParams.set('current_weather', 'true');

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error('Weather fetch failed');
  const data = await res.json();
  return data.current_weather || null;
}

function renderWeather({ place, tempC, code }) {
  placeEl.textContent = place;
  tempEl.textContent = `${Math.round(tempC)}°C`;
  const { description, emoji } = weatherCodeToDescIcon(code);
  descEl.textContent = description;
  iconEl.textContent = emoji;
  card.hidden = false;
}

// Map Open-Meteo WMO weather codes to a simple description and emoji icon
// Source: https://open-meteo.com/en/docs (Weather codes)
function weatherCodeToDescIcon(code) {
  const map = {
    0: ['Clear sky', '☀️'],
    1: ['Mainly clear', '🌤️'],
    2: ['Partly cloudy', '⛅'],
    3: ['Overcast', '☁️'],
    45: ['Fog', '🌫️'],
    48: ['Depositing rime fog', '🌫️'],
    51: ['Light drizzle', '🌦️'],
    53: ['Moderate drizzle', '🌦️'],
    55: ['Dense drizzle', '🌦️'],
    56: ['Light freezing drizzle', '🌧️'],
    57: ['Dense freezing drizzle', '🌧️'],
    61: ['Slight rain', '🌧️'],
    63: ['Moderate rain', '🌧️'],
    65: ['Heavy rain', '🌧️'],
    66: ['Light freezing rain', '🌧️'],
    67: ['Heavy freezing rain', '🌧️'],
    71: ['Slight snow', '🌨️'],
    73: ['Moderate snow', '🌨️'],
    75: ['Heavy snow', '❄️'],
    77: ['Snow grains', '🌨️'],
    80: ['Rain showers: slight', '🌦️'],
    81: ['Rain showers: moderate', '🌦️'],
    82: ['Rain showers: violent', '🌧️'],
    85: ['Snow showers: slight', '🌨️'],
    86: ['Snow showers: heavy', '❄️'],
    95: ['Thunderstorm', '⛈️'],
    96: ['Thunderstorm with slight hail', '⛈️'],
    99: ['Thunderstorm with heavy hail', '⛈️']
  };
  if (code in map) {
    const [description, emoji] = map[code];
    return { description, emoji };
  }
  return { description: 'Unknown', emoji: '❓' };
}

