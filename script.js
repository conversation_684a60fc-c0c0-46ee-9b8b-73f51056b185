// Weather app using OpenWeather API
// Get your free API key at: https://openweathermap.org/api
// Flow:
// 1) Geocode the user-entered place to lat/lon via OpenWeather geocoding API
// 2) Fetch current weather via OpenWeather current weather API
// 3) Display comprehensive weather data including humidity, pressure, wind, etc.

// Configuration - Replace 'YOUR_API_KEY_HERE' with your actual OpenWeather API key
const OPENWEATHER_API_KEY = '********************************';

// Check if API key is configured
if (OPENWEATHER_API_KEY === 'YOUR_API_KEY_HERE') {
  console.warn('⚠️ Please configure your OpenWeather API key in script.js');
  console.warn('Get your free API key at: https://openweathermap.org/api');
}

const form = document.getElementById('weatherForm');
const input = document.getElementById('locationInput');
const statusEl = document.getElementById('status');
const resultsEl = document.getElementById('results');
const card = document.getElementById('weather');
const placeEl = document.getElementById('place');
const tempEl = document.getElementById('temp');
const feelsLikeEl = document.getElementById('feels-like');
const descEl = document.getElementById('desc');
const iconEl = document.getElementById('icon');
const humidityEl = document.getElementById('humidity');
const pressureEl = document.getElementById('pressure');
const windEl = document.getElementById('wind');
const visibilityEl = document.getElementById('visibility');
const sunriseEl = document.getElementById('sunrise');
const sunsetEl = document.getElementById('sunset');

form.addEventListener('submit', async (e) => {
  e.preventDefault();
  const q = input.value.trim();
  if (!q) return;

  resetUI();
  setStatus('Searching…');

  try {
    const choices = await geocodeMany(q);
    if (!choices || choices.length === 0) {
      setStatus('No results. Try a different place.');
      return;
    }

    if (choices.length === 1) {
      selectPlace(choices[0]);
      return;
    }

    // Show chooser
    showResults(choices);
    setStatus('Select a location');
  } catch (err) {
    console.error('Error details:', err);
    setStatus(`Error: ${err.message}`);
  }
});

function resetUI() {
  card.hidden = true;
  hideResults();
  iconEl.src = '';
  iconEl.alt = 'Weather icon';
  placeEl.textContent = '—';
  tempEl.textContent = '—';
  feelsLikeEl.textContent = 'Feels like —';
  descEl.textContent = '—';
  humidityEl.textContent = '—';
  pressureEl.textContent = '—';
  windEl.textContent = '—';
  visibilityEl.textContent = '—';
  sunriseEl.textContent = '—';
  sunsetEl.textContent = '—';
}

function setStatus(msg) {
  statusEl.textContent = msg;
}

function showResults(choices) {
  resultsEl.innerHTML = '';
  resultsEl.hidden = false;
  choices.slice(0, 8).forEach((c, idx) => {
    const li = document.createElement('li');
    li.tabIndex = 0;
    li.role = 'option';
    li.ariaSelected = 'false';
    li.textContent = formatPlace(c);
    li.addEventListener('click', () => selectPlace(c));
    li.addEventListener('keydown', (ev) => {
      if (ev.key === 'Enter' || ev.key === ' ') selectPlace(c);
    });
    resultsEl.appendChild(li);
    if (idx === 0) li.focus();
  });
}

function hideResults() {
  resultsEl.hidden = true;
  resultsEl.innerHTML = '';
}

function formatPlace(c) {
  const parts = [c.name, c.state, c.country].filter(Boolean);
  return parts.join(', ');
}

async function geocodeMany(query) {
  if (!OPENWEATHER_API_KEY || OPENWEATHER_API_KEY === 'YOUR_API_KEY_HERE') {
    throw new Error('Please configure your OpenWeather API key');
  }

  const url = new URL('https://api.openweathermap.org/geo/1.0/direct');
  url.searchParams.set('q', query);
  url.searchParams.set('limit', '5');
  url.searchParams.set('appid', OPENWEATHER_API_KEY);

  const res = await fetch(url.toString());
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`Geocoding failed: ${res.status} ${res.statusText} - ${errorText}`);
  }
  const data = await res.json();
  return data || [];
}

async function selectPlace(geo) {
  hideResults();
  const { name, country, lat, lon, state } = geo;
  setStatus('Fetching weather…');

  try {
    const weather = await getCurrentWeather(lat, lon);
    if (!weather) {
      setStatus('Could not fetch weather.');
      return;
    }

    const placeParts = [name, state, country].filter(Boolean);
    renderWeather({
      place: placeParts.join(', '),
      weather: weather
    });
    setStatus('');
  } catch (err) {
    console.error('Weather fetch error:', err);
    setStatus(`Weather error: ${err.message}`);
  }
}



async function getCurrentWeather(lat, lon) {
  if (!OPENWEATHER_API_KEY || OPENWEATHER_API_KEY === 'YOUR_API_KEY_HERE') {
    throw new Error('Please configure your OpenWeather API key');
  }

  const url = new URL('https://api.openweathermap.org/data/2.5/weather');
  url.searchParams.set('lat', String(lat));
  url.searchParams.set('lon', String(lon));
  url.searchParams.set('appid', OPENWEATHER_API_KEY);
  url.searchParams.set('units', 'metric');

  const res = await fetch(url.toString());
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`Weather fetch failed: ${res.status} ${res.statusText} - ${errorText}`);
  }
  const data = await res.json();
  return data;
}

function renderWeather({ place, weather }) {
  placeEl.textContent = place;

  // Main weather info
  tempEl.textContent = `${Math.round(weather.main.temp)}°C`;
  feelsLikeEl.textContent = `Feels like ${Math.round(weather.main.feels_like)}°C`;
  descEl.textContent = weather.weather[0].description;

  // Weather icon from OpenWeather
  const iconCode = weather.weather[0].icon;
  iconEl.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
  iconEl.alt = weather.weather[0].description;

  // Weather details
  humidityEl.textContent = `${weather.main.humidity}%`;
  pressureEl.textContent = `${weather.main.pressure} hPa`;

  // Wind information
  const windSpeed = weather.wind?.speed || 0;
  const windDir = weather.wind?.deg;
  let windText = `${windSpeed} m/s`;
  if (windDir !== undefined) {
    windText += ` ${getWindDirection(windDir)}`;
  }
  windEl.textContent = windText;

  // Visibility
  const visibility = weather.visibility ? (weather.visibility / 1000).toFixed(1) : 'N/A';
  visibilityEl.textContent = visibility !== 'N/A' ? `${visibility} km` : 'N/A';

  // Sunrise and sunset
  if (weather.sys?.sunrise && weather.sys?.sunset) {
    const sunrise = new Date(weather.sys.sunrise * 1000);
    const sunset = new Date(weather.sys.sunset * 1000);
    sunriseEl.textContent = sunrise.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    sunsetEl.textContent = sunset.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else {
    sunriseEl.textContent = 'N/A';
    sunsetEl.textContent = 'N/A';
  }

  card.hidden = false;
}

// Helper function to convert wind direction degrees to compass direction
function getWindDirection(degrees) {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
  const index = Math.round(degrees / 22.5) % 16;
  return directions[index];
}

